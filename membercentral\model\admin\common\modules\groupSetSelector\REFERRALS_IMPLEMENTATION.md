# Group Set Selector Referrals Support Implementation

## Overview
This implementation extends the existing group set selector to support referrals classifications while maintaining full backward compatibility with all existing usage.

## Key Changes Made

### 1. groupSetSelector.cfc

#### getMultipleGroupSetSelector Method
- Added optional `referralID` parameter (default: 0)
- Changed `siteResourceID` from required to optional (default: 0)
- Maintains backward compatibility by keeping existing parameter structure

#### getAvailableAndSelectedGroupSetsJSON Method
- Added optional `referralID` parameter (default: 0)
- Added optional `selectorID` parameter (default: "")
- Changed `siteResourceID` from required to optional (default: 0)
- Implemented context detection logic using case-insensitive search for 'referral' in selectorID
- Added parameter validation based on context
- Uses `ref_classifications` table for referrals context
- Uses `ams_classifications` table for standard context

#### moveGroupSet Method
- Added optional `selectorID` parameter for context detection
- Added optional `mcproxy_memberID` parameter for referrals audit logging
- Uses `ref_moveClassifications` stored procedure for referrals context
- Uses `ams_moveClassifications` stored procedure for standard context

### 2. dsp_groupsets_multiple.cfm

#### JavaScript AJAX Calls
- Updated `getGroupSetsJSON_` function to pass `selectorID` parameter
- Added conditional inclusion of `referralID` parameter when available
- Updated `moveGroupSetUp_` and `moveGroupSetDown_` functions to pass `selectorID`

## Context Detection Logic

The implementation uses case-insensitive detection of the word 'referral' in the `selectorID` parameter:

```coldfusion
<cfset local.isReferralsContext = (len(trim(arguments.selectorID)) AND findNoCase("referral", arguments.selectorID))>
```

### Examples:
- `"memberSettingsGroupSets"` → Standard context
- `"referralClassificationsGroupSets"` → Referrals context  
- `"ReferralGroupSets"` → Referrals context
- `"projectDetailsGroupSets"` → Standard context

## Database Table Usage

### Standard Context (ams_classifications)
```sql
SELECT c.classificationID, c.name as classificationName, c.groupSetID, c.classificationOrder,
       gs.groupSetName
FROM ams_classifications c
INNER JOIN ams_memberGroupSets gs ON c.groupSetID = gs.groupSetID
WHERE c.siteResourceID = ? AND gs.orgID = ?
  AND c.area = ? -- optional area filter
ORDER BY c.classificationOrder, c.name
```

### Referrals Context (ref_classifications)
```sql
SELECT c.classificationID, c.name as classificationName, c.groupSetID, c.classificationOrder,
       gs.groupSetName
FROM ref_classifications c
INNER JOIN ams_memberGroupSets gs ON c.groupSetID = gs.groupSetID
WHERE c.referralID = ? AND gs.orgID = ?
ORDER BY c.classificationOrder, c.name
```

## Usage Examples

### Standard Context (Existing - Unchanged)
```coldfusion
local.groupSetWidget = objGroupSetSelector.getMultipleGroupSetSelector(
    siteID = siteID,
    orgID = orgID,
    selectorID = "memberSettingsGroupSets",
    siteResourceID = memberAdminSiteResourceID,
    selectedGSGridHeight = 200,
    area = "details",
    editClassificationToolType = "MemberSettingsAdmin"
);
```

### Referrals Context (New)
```coldfusion
local.referralsGroupSetWidget = objGroupSetSelector.getMultipleGroupSetSelector(
    siteID = siteID,
    orgID = orgID,
    selectorID = "referralClassificationsGroupSets", // Contains 'referral'
    referralID = referralID, // Required for referrals context
    selectedGSGridHeight = 200,
    editClassificationToolType = "ReferralsAdmin"
);
```

## Parameter Validation

The implementation includes robust parameter validation:

- **Referrals Context**: Requires `referralID` parameter
- **Standard Context**: Requires `siteResourceID` parameter
- Returns error response if required parameters are missing

## Backward Compatibility

✅ **Fully Backward Compatible**
- All existing usage continues working unchanged
- No breaking changes to existing APIs
- Existing parameter requirements maintained for standard context
- All existing functionality preserved

## Testing

The implementation includes:
- Context detection testing
- Parameter validation testing
- AJAX call parameter testing
- Database query testing for both contexts

## Future Extensions

This implementation provides a foundation for future extensions:
- Member directory classifications support can be added using similar patterns
- Additional context types can be easily integrated
- The pattern can be extended to other classification systems

## Files Modified

1. `membercentral/model/admin/common/modules/groupSetSelector/groupSetSelector.cfc`
2. `membercentral/model/admin/common/modules/groupSetSelector/dsp_groupsets_multiple.cfm`

## Files Added

1. `membercentral/model/admin/common/modules/groupSetSelector/test_referrals_support.cfm`
2. `membercentral/model/admin/common/modules/groupSetSelector/REFERRALS_IMPLEMENTATION.md`
