<!---
Example of how to integrate the group set selector into ReferralsAdmin.cfc
This shows the method structure and how to call the group set selector
--->

<cfcomponent>
	
	<!--- Example method showing how to integrate group set selector into referrals admin --->
	<cffunction name="manageReferralGroupSets" access="public" output="false" returntype="struct" hint="Manage referral group set classifications">
		<cfargument name="Event" type="any" />
		
		<cfscript>
			var local = structNew();
			
			// Get referral context information
			local.referralID = this.referralID; // Available in ReferralsAdmin.cfc
			local.siteID = arguments.event.getValue('mc_siteInfo.siteID');
			local.orgID = arguments.event.getValue('mc_siteInfo.orgID');
			
			// Create group set selector instance
			local.objGroupSetSelector = createObject("component","model.admin.common.modules.groupSetSelector.groupSetSelector");
			
			// Get the referrals group set selector widget
			local.referralsGroupSetWidget = local.objGroupSetSelector.getMultipleGroupSetSelector(
				siteID = local.siteID,
				orgID = local.orgID,
				selectorID = "referralClassificationsGroupSets", // Contains 'referral' - triggers referrals context
				referralID = local.referralID, // Required for referrals context
				selectedGSGridHeight = 350,
				editClassificationToolType = "ReferralsAdmin" // Uses ReferralsAdmin for edit classification
			);
			
			// Build breadcrumb trail
			appendBreadCrumbs(arguments.event, { link='', text='Group Set Classifications' });
		</cfscript>

		<cfsavecontent variable="local.data">
			<cfoutput>
				<cfinclude template="dsp_referral_groupset_management.cfm">
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<!--- Example method showing how to add group set selector to existing referrals settings --->
	<cffunction name="enhancedManageMainSettings" access="public" output="false" returntype="struct" hint="Enhanced main settings with group set selector">
		<cfargument name="Event" type="any" />
		
		<cfscript>
			var local = structNew();
			
			// Existing referrals settings logic would go here...
			// ... (existing code from manageMainSettings method)
			
			// Add group set selector for referrals classifications
			local.objGroupSetSelector = createObject("component","model.admin.common.modules.groupSetSelector.groupSetSelector");
			
			local.referralsGroupSetWidget = local.objGroupSetSelector.getMultipleGroupSetSelector(
				siteID = arguments.event.getValue('mc_siteInfo.siteID'),
				orgID = arguments.event.getValue('mc_siteInfo.orgID'),
				selectorID = "referralMainSettingsGroupSets", // Contains 'referral' - triggers referrals context
				referralID = this.referralID,
				selectedGSGridHeight = 250,
				editClassificationToolType = "ReferralsAdmin"
			);
			
			// Build breadcrumb trail
			appendBreadCrumbs(arguments.event, { link='', text='Main Settings' });
		</cfscript>

		<cfsavecontent variable="local.data">
			<cfoutput>
				<cfinclude template="frm_enhanced_mainSettings.cfm">
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
</cfcomponent>
