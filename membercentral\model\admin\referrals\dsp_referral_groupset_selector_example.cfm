<!---
Example implementation of referrals group set selector
This shows how to integrate the group set selector into referrals admin pages
--->

<cfscript>
	// Example referrals context parameters
	local.referralID = this.referralID; // From ReferralsAdmin.cfc
	local.siteID = arguments.event.getValue('mc_siteInfo.siteID');
	local.orgID = arguments.event.getValue('mc_siteInfo.orgID');
	
	// Create group set selector for referrals
	local.objGroupSetSelector = createObject("component","model.admin.common.modules.groupSetSelector.groupSetSelector");
	
	// Get the referrals group set selector widget
	local.referralsGroupSetWidget = local.objGroupSetSelector.getMultipleGroupSetSelector(
		siteID = local.siteID,
		orgID = local.orgID,
		selectorID = "referralClassificationsGroupSets", // Contains 'referral' - triggers referrals context
		referralID = local.referralID, // Required for referrals context
		selectedGSGridHeight = 300,
		editClassificationToolType = "ReferralsAdmin" // Uses ReferralsAdmin for edit classification
	);
</cfscript>

<cfoutput>
<div class="container-fluid">
	<div class="row">
		<div class="col-12">
			<div class="card">
				<div class="card-header bg-primary text-white">
					<h5 class="mb-0">
						<i class="fa-solid fa-layer-group"></i>
						Referral Classifications Management
					</h5>
				</div>
				<div class="card-body">
					<div class="alert alert-info">
						<strong>Instructions:</strong> Use this interface to manage group set classifications for this referral. 
						You can add group sets from the available list, edit classification settings, and reorder them as needed.
					</div>
					
					<!--- Render the referrals group set selector --->
					#local.referralsGroupSetWidget.html#
					
					<div class="mt-3">
						<h6>Context Information:</h6>
						<ul class="list-unstyled">
							<li><strong>Referral ID:</strong> #local.referralID#</li>
							<li><strong>Selector ID:</strong> referralClassificationsGroupSets</li>
							<li><strong>Context:</strong> Referrals (detected automatically)</li>
							<li><strong>Database Table:</strong> ref_classifications</li>
							<li><strong>Edit Tool:</strong> ReferralsAdmin</li>
						</ul>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>

<script>
$(document).ready(function() {
	// Additional referrals-specific JavaScript can go here
	console.log('Referrals Group Set Selector initialized for referralID: #local.referralID#');
	
	// Example of how to refresh the selector after external changes
	window.refreshReferralGroupSets = function() {
		if (typeof loadGroupSetGrids_referralClassificationsGroupSets === 'function') {
			loadGroupSetGrids_referralClassificationsGroupSets();
		}
	};
});
</script>
</cfoutput>
