<!---
Test file to demonstrate referrals support in group set selector
This file shows how to use the extended group set selector for referrals context
--->

<cfscript>
	// Test parameters for referrals context
	local.testReferralID = 123; // Example referral ID
	local.testSiteID = 1;
	local.testOrgID = 1;
	
	// Create group set selector instance
	local.objGroupSetSelector = createObject("component","model.admin.common.modules.groupSetSelector.groupSetSelector");
	
	// Example 1: Standard context (existing functionality - should work unchanged)
	local.standardGroupSetWidget = local.objGroupSetSelector.getMultipleGroupSetSelector(
		siteID = local.testSiteID,
		orgID = local.testOrgID,
		selectorID = "memberSettingsGroupSets",
		siteResourceID = 456, // Example siteResourceID
		selectedGSGridHeight = 200,
		area = "details",
		editClassificationToolType = "MemberSettingsAdmin"
	);
	
	// Example 2: Referrals context (new functionality)
	local.referralsGroupSetWidget = local.objGroupSetSelector.getMultipleGroupSetSelector(
		siteID = local.testSiteID,
		orgID = local.testOrgID,
		selectorID = "referralClassificationsGroupSets", // Contains 'referral' - triggers referrals context
		referralID = local.testReferralID, // Required for referrals context
		selectedGSGridHeight = 200,
		editClassificationToolType = "ReferralsAdmin"
	);
	
	// Example 3: Test AJAX call parameters for referrals context
	local.testAjaxParams = {
		"mcproxy_siteID": local.testSiteID,
		"mcproxy_orgID": local.testOrgID,
		"selectorID": "referralClassificationsGroupSets",
		"referralID": local.testReferralID
	};
	
	// Example 4: Test AJAX call parameters for standard context  
	local.testStandardAjaxParams = {
		"mcproxy_siteID": local.testSiteID,
		"mcproxy_orgID": local.testOrgID,
		"selectorID": "memberSettingsGroupSets",
		"siteResourceID": 456,
		"area": "details"
	};
</cfscript>

<!--- Display test results --->
<cfoutput>
<h2>Group Set Selector Referrals Support Test</h2>

<h3>1. Standard Context Widget (Existing Functionality)</h3>
<p><strong>Selector ID:</strong> memberSettingsGroupSets</p>
<p><strong>Context Detection:</strong> Standard (no 'referral' in selectorID)</p>
<p><strong>Required Parameters:</strong> siteResourceID</p>
<p><strong>Table Used:</strong> ams_classifications</p>
<div class="border p-2 mb-3">
	#local.standardGroupSetWidget.html#
</div>

<h3>2. Referrals Context Widget (New Functionality)</h3>
<p><strong>Selector ID:</strong> referralClassificationsGroupSets</p>
<p><strong>Context Detection:</strong> Referrals ('referral' found in selectorID)</p>
<p><strong>Required Parameters:</strong> referralID</p>
<p><strong>Table Used:</strong> ref_classifications</p>
<div class="border p-2 mb-3">
	#local.referralsGroupSetWidget.html#
</div>

<h3>3. AJAX Parameters Test</h3>
<h4>Referrals Context AJAX Parameters:</h4>
<pre>#serializeJSON(local.testAjaxParams)#</pre>

<h4>Standard Context AJAX Parameters:</h4>
<pre>#serializeJSON(local.testStandardAjaxParams)#</pre>

<h3>4. Context Detection Logic Test</h3>
<cfset local.testSelectorIDs = [
	"memberSettingsGroupSets",
	"projectDetailsGroupSets", 
	"referralClassificationsGroupSets",
	"ReferralGroupSets",
	"indyMatchSettingsGroupSets",
	"someReferralSelector"
]>

<table class="table table-bordered">
	<thead>
		<tr>
			<th>Selector ID</th>
			<th>Contains 'referral'?</th>
			<th>Detected Context</th>
		</tr>
	</thead>
	<tbody>
		<cfloop array="#local.testSelectorIDs#" index="selectorID">
			<cfset local.isReferrals = findNoCase("referral", selectorID)>
			<tr>
				<td>#selectorID#</td>
				<td>#local.isReferrals GT 0 ? "Yes" : "No"#</td>
				<td>#local.isReferrals GT 0 ? "Referrals" : "Standard"#</td>
			</tr>
		</cfloop>
	</tbody>
</table>

<h3>5. Function Context Handling Test</h3>
<h4>removeGroupSetFromSelected Function:</h4>
<ul>
	<li><strong>Standard Context:</strong> Uses TS_AJX('ADMMEMBERSETTINGS','deleteClassification')</li>
	<li><strong>Referrals Context:</strong> Uses TS_AJX('ADMINREFERRALS','deleteClassification')</li>
</ul>

<h4>editClassification Function:</h4>
<ul>
	<li><strong>Standard Context:</strong> Uses #arguments.editClassificationToolType# tool with siteResourceID parameter</li>
	<li><strong>Referrals Context:</strong> Uses ReferralsAdmin tool with referralID parameter</li>
</ul>

<h4>previewGroupSet Function:</h4>
<ul>
	<li><strong>Both Contexts:</strong> Uses same previewGroupSetLink (group sets are shared)</li>
</ul>

<h3>6. Implementation Summary</h3>
<ul>
	<li><strong>Backward Compatibility:</strong> All existing usage continues working unchanged</li>
	<li><strong>Context Detection:</strong> Case-insensitive detection of 'referral' in selectorID</li>
	<li><strong>Parameter Validation:</strong> Requires referralID for referrals context, siteResourceID for standard context</li>
	<li><strong>Database Tables:</strong> Uses ref_classifications for referrals, ams_classifications for standard</li>
	<li><strong>Move Operations:</strong> Uses ref_moveClassifications for referrals, ams_moveClassifications for standard</li>
	<li><strong>Delete Operations:</strong> Uses ADMINREFERRALS component for referrals, ADMMEMBERSETTINGS for standard</li>
	<li><strong>Edit Operations:</strong> Uses ReferralsAdmin tool for referrals, configurable tool for standard</li>
</ul>
</cfoutput>
